# Dynamic Feature-wise Routing System

## Overview

This document describes the implementation of a dynamic, feature-wise routing system for the Kirana Shop Express backend. The system automatically discovers and loads route modules, providing a scalable architecture that keeps routes organized and maintainable.

## Architecture

### 1. Core Components

#### Route Loader (`src/utils/routeLoader.ts`)
- **Purpose**: Dynamically discovers and loads route modules from feature directories
- **Features**:
  - Automatic route discovery
  - Feature-based organization
  - Middleware support per feature
  - Comprehensive logging
  - Error handling

#### Type Definitions (`src/types/routes.types.ts`)
- **Purpose**: Provides TypeScript interfaces for consistent route structure
- **Key Interfaces**:
  - `FeatureRoutes`: Main route module structure
  - `RouteConfig`: Route configuration options
  - `ApiResponse`: Standardized API response format
  - `PaginationParams`: Pagination support

#### Middleware (`src/middlewares/`)
- **Route Logger**: Logs all incoming requests with detailed information
- **Error Handler**: Global error handling with custom error types
- **Async Handler**: Wrapper for async route handlers

### 2. Directory Structure

```
backend/src/routes/
├── index.ts                    # Main route setup and API documentation
├── auth/
│   └── auth.routes.ts         # Authentication routes
├── products/
│   └── products.routes.ts     # Product management routes
├── inventory/
│   └── inventory.routes.ts    # Inventory management routes
├── sales/
│   └── sales.routes.ts        # Sales management routes
├── customers/
│   └── customers.routes.ts    # Customer management routes
└── reports/
    └── reports.routes.ts      # Reports and analytics routes
```

## Features

### 1. Authentication Routes (`/api/v1/auth`)
- `POST /login` - User authentication
- `POST /register` - User registration
- `POST /logout` - User logout
- `GET /profile` - Get user profile
- `PUT /change-password` - Change password
- `POST /forgot-password` - Password reset

### 2. Products Routes (`/api/v1/products`)
- `GET /` - List products with pagination and filters
- `GET /:id` - Get product by ID
- `POST /` - Create new product
- `PUT /:id` - Update product
- `DELETE /:id` - Delete product
- `GET /categories/list` - Get product categories
- `GET /barcode/:barcode` - Search by barcode

### 3. Inventory Routes (`/api/v1/inventory`)
- `GET /overview` - Inventory overview and statistics
- `GET /low-stock` - Get low stock items
- `GET /out-of-stock` - Get out of stock items
- `POST /adjust` - Stock adjustment (in/out)
- `GET /movements` - Stock movement history
- `POST /bulk-update` - Bulk stock updates

### 4. Sales Routes (`/api/v1/sales`)
- `GET /` - List sales with pagination
- `GET /:id` - Get sale by ID
- `POST /` - Create new sale
- `PATCH /:id/status` - Update sale status
- `GET /analytics/summary` - Sales analytics
- `POST /:id/refund` - Process refund
- `GET /reports/daily` - Daily sales report

### 5. Customers Routes (`/api/v1/customers`)
- `GET /` - List customers with pagination
- `GET /:id` - Get customer by ID
- `POST /` - Create new customer
- `PUT /:id` - Update customer
- `DELETE /:id` - Delete customer
- `GET /:id/purchases` - Customer purchase history
- `GET /:id/analytics` - Customer analytics
- `GET /search/phone/:phone` - Search by phone
- `GET /analytics/top` - Top customers

### 6. Reports Routes (`/api/v1/reports`)
- `GET /dashboard` - Dashboard overview
- `GET /sales` - Sales reports
- `GET /inventory` - Inventory reports
- `GET /customers` - Customer reports
- `GET /profit-loss` - Profit/Loss reports
- `GET /tax` - Tax reports
- `POST /export` - Export reports
- `GET /download/:jobId` - Download exported reports

## Key Features

### 1. Automatic Route Discovery
The system automatically scans the `routes` directory and loads all `.routes.ts` files:

```typescript
// Routes are automatically discovered and loaded
const routeLoader = new RouteLoader(app);
await routeLoader.loadRoutes();
```

### 2. Feature-based Organization
Each feature has its own directory with dedicated route files:

```typescript
const authRoutes: FeatureRoutes = {
    config: {
        path: '/api/v1/auth',
        router: setupAuthRoutes(),
        middleware: [detailedLogger('auth')],
        description: 'Authentication routes',
        version: '1.0.0'
    },
    setup: setupAuthRoutes
};
```

### 3. Standardized API Responses
All routes use consistent response format:

```typescript
interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    timestamp: string;
}
```

### 4. Comprehensive Error Handling
- Custom error classes with status codes
- Async error wrapper for route handlers
- Global error middleware
- Development vs production error responses

### 5. Request Logging
- Detailed request/response logging
- Sensitive data redaction
- Response time tracking
- Feature-specific logging

### 6. 404 Handling
- Catch-all route for undefined endpoints
- Consistent error response format
- Helpful error messages

## Usage

### Adding New Feature Routes

1. **Create feature directory**:
   ```bash
   mkdir backend/src/routes/newfeature
   ```

2. **Create route file**:
   ```typescript
   // backend/src/routes/newfeature/newfeature.routes.ts
   import { Router, Request, Response } from 'express';
   import { FeatureRoutes } from '../../types/routes.types';

   const setupNewFeatureRoutes = (): Router => {
       const router = Router();

       router.get('/', (req, res) => {
           res.json(successResponse('New feature endpoint'));
       });

       return router;
   };

   const newFeatureRoutes: FeatureRoutes = {
       config: {
           path: '/api/v1/newfeature',
           router: setupNewFeatureRoutes(),
           middleware: [detailedLogger('newfeature')],
           description: 'New feature routes',
           version: '1.0.0'
       },
       setup: setupNewFeatureRoutes
   };

   export default newFeatureRoutes;
   ```

3. **Routes are automatically loaded** - No additional configuration needed!

### API Documentation

Access API documentation at:
- `GET /api/v1` - API information
- `GET /api/v1/docs` - Route documentation
- `GET /health` - Health check

## Benefits

1. **Scalability**: Easy to add new features without modifying core routing logic
2. **Organization**: Clear separation of concerns by feature
3. **Maintainability**: Consistent structure and patterns
4. **Type Safety**: Full TypeScript support with interfaces
5. **Error Handling**: Comprehensive error management
6. **Logging**: Detailed request/response logging
7. **Documentation**: Self-documenting API endpoints

## Testing

Test the routing system:

1. **Start the server**:
   ```bash
   cd backend
   npm run dev
   ```

2. **Test endpoints**:
   ```bash
   # API info
   curl http://localhost:5000/api/v1

   # Health check
   curl http://localhost:5000/health

   # Feature endpoints
   curl http://localhost:5000/api/v1/products
   curl http://localhost:5000/api/v1/auth/profile
   ```

3. **Test 404 handling**:
   ```bash
   curl http://localhost:5000/api/v1/nonexistent
   ```

## Implementation Status

### ✅ Completed
- [x] Dynamic route loading system
- [x] Feature-wise route organization
- [x] TypeScript interfaces and type safety
- [x] Comprehensive error handling
- [x] Request/response logging
- [x] 404 handling for undefined routes
- [x] Standardized API response format
- [x] Mock data for all endpoints
- [x] Pagination support
- [x] API documentation endpoints

### 🔄 Next Steps
1. **Install missing dependency**: `npm install --save-dev @types/cors`
2. **Database Integration**: Replace mock data with actual database operations
3. **Authentication**: Implement JWT-based authentication middleware
4. **Validation**: Add request validation using libraries like Joi or Yup
5. **Testing**: Write unit and integration tests for all routes

### 🚀 Future Enhancements
1. **Rate Limiting**: Implement rate limiting per feature
2. **API Versioning**: Support multiple API versions
3. **OpenAPI Documentation**: Generate Swagger documentation
4. **Route Caching**: Cache route responses where appropriate
5. **Metrics**: Add performance metrics and monitoring
6. **File Upload**: Add file upload capabilities for product images
7. **Real-time Updates**: WebSocket support for real-time inventory updates
