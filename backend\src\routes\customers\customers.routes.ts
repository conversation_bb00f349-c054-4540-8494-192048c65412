import { Router, Request, Response } from 'express';
import { FeatureRoutes } from '../../types/routes.types';
import { asyncHandler } from '../../middlewares/errorHandler';
import { detailedLogger } from '../../middlewares/routeLogger';
import { successResponse, errorResponse } from '../../utils/responseHelpers';

/**
 * Customers routes setup
 */
const setupCustomersRoutes = (): Router => {
    const router = Router();

    // Get all customers with pagination
    router.get('/', asyncHandler(async (req: Request, res: Response) => {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        const search = req.query.search as string || '';

        const mockCustomers = [
            {
                id: 1,
                name: '<PERSON>',
                phone: '+91-9876543210',
                email: '<EMAIL>',
                address: '123 Main Street, City',
                totalPurchases: 15,
                totalSpent: 4500.50,
                lastPurchase: '2024-01-20T10:30:00Z',
                createdAt: '2024-01-01T00:00:00Z'
            },
            {
                id: 2,
                name: 'Jane Smith',
                phone: '+91-9876543211',
                email: '<EMAIL>',
                address: '456 Oak Avenue, City',
                totalPurchases: 8,
                totalSpent: 2400.75,
                lastPurchase: '2024-01-19T15:45:00Z',
                createdAt: '2024-01-05T00:00:00Z'
            }
        ];

        // Apply search filter
        let filteredCustomers = mockCustomers;
        if (search) {
            filteredCustomers = mockCustomers.filter(c =>
                c.name.toLowerCase().includes(search.toLowerCase()) ||
                c.phone.includes(search) ||
                c.email.toLowerCase().includes(search.toLowerCase())
            );
        }

        const total = filteredCustomers.length;
        const startIndex = (page - 1) * limit;
        const paginatedCustomers = filteredCustomers.slice(startIndex, startIndex + limit);

        res.json(successResponse('Customers retrieved successfully', {
            customers: paginatedCustomers,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: startIndex + limit < total,
                hasPrev: page > 1
            }
        }));
    }));

    // Get customer by ID
    router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;

        const mockCustomer = {
            id: parseInt(id),
            name: 'John Doe',
            phone: '+91-9876543210',
            email: '<EMAIL>',
            address: '123 Main Street, City',
            dateOfBirth: '1985-06-15',
            totalPurchases: 15,
            totalSpent: 4500.50,
            averageOrderValue: 300.03,
            lastPurchase: '2024-01-20T10:30:00Z',
            favoriteProducts: [
                { productId: 1, productName: 'Rice (Basmati)', purchaseCount: 5 },
                { productId: 2, productName: 'Milk', purchaseCount: 8 }
            ],
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-20T10:30:00Z'
        };

        res.json(successResponse('Customer retrieved successfully', mockCustomer));
    }));

    // Create new customer
    router.post('/', asyncHandler(async (req: Request, res: Response) => {
        const { name, phone, email, address, dateOfBirth } = req.body;

        // Validation
        if (!name || !phone) {
            return res.status(400).json(
                errorResponse('Name and phone are required')
            );
        }

        // Mock created customer
        const newCustomer = {
            id: Date.now(),
            name,
            phone,
            email: email || '',
            address: address || '',
            dateOfBirth: dateOfBirth || null,
            totalPurchases: 0,
            totalSpent: 0,
            lastPurchase: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        res.status(201).json(successResponse('Customer created successfully', newCustomer));
    }));

    // Update customer
    router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;
        const updateData = req.body;

        // Mock updated customer
        const updatedCustomer = {
            id: parseInt(id),
            ...updateData,
            updatedAt: new Date().toISOString()
        };

        res.json(successResponse('Customer updated successfully', updatedCustomer));
    }));

    // Delete customer
    router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;

        // TODO: Check if customer has any sales before deletion
        res.json(successResponse(`Customer with ID ${id} deleted successfully`));
    }));

    // Get customer purchase history
    router.get('/:id/purchases', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;

        const mockPurchases = [
            {
                id: 1,
                invoiceNumber: 'INV-001',
                items: [
                    { productName: 'Rice (Basmati)', quantity: 2, price: 120, total: 240 },
                    { productName: 'Milk', quantity: 1, price: 60, total: 60 }
                ],
                total: 330,
                paymentMethod: 'cash',
                purchaseDate: '2024-01-20T10:30:00Z'
            },
            {
                id: 2,
                invoiceNumber: 'INV-005',
                items: [
                    { productName: 'Sugar', quantity: 1, price: 45, total: 45 }
                ],
                total: 49.5,
                paymentMethod: 'card',
                purchaseDate: '2024-01-18T14:15:00Z'
            }
        ];

        const total = mockPurchases.length;
        const startIndex = (page - 1) * limit;
        const paginatedPurchases = mockPurchases.slice(startIndex, startIndex + limit);

        res.json(successResponse('Customer purchase history retrieved successfully', {
            customerId: parseInt(id),
            purchases: paginatedPurchases,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: startIndex + limit < total,
                hasPrev: page > 1
            }
        }));
    }));

    // Get customer analytics
    router.get('/:id/analytics', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;

        const mockAnalytics = {
            customerId: parseInt(id),
            totalPurchases: 15,
            totalSpent: 4500.50,
            averageOrderValue: 300.03,
            firstPurchase: '2024-01-01T10:00:00Z',
            lastPurchase: '2024-01-20T10:30:00Z',
            favoriteCategories: [
                { category: 'Grains', purchaseCount: 8, totalSpent: 2400 },
                { category: 'Dairy', purchaseCount: 12, totalSpent: 1800 }
            ],
            monthlySpending: [
                { month: '2024-01', amount: 4500.50, purchases: 15 }
            ],
            loyaltyScore: 85, // Based on frequency and amount
            recommendedProducts: [
                { productId: 3, productName: 'Cooking Oil', reason: 'Frequently bought together' }
            ]
        };

        res.json(successResponse('Customer analytics retrieved successfully', mockAnalytics));
    }));

    // Search customers by phone
    router.get('/search/phone/:phone', asyncHandler(async (req: Request, res: Response) => {
        const { phone } = req.params;

        const mockCustomer = {
            id: 1,
            name: 'John Doe',
            phone,
            email: '<EMAIL>',
            totalPurchases: 15,
            totalSpent: 4500.50,
            lastPurchase: '2024-01-20T10:30:00Z'
        };

        res.json(successResponse('Customer found', mockCustomer));
    }));

    // Get top customers
    router.get('/analytics/top', asyncHandler(async (req: Request, res: Response) => {
        const limit = parseInt(req.query.limit as string) || 10;
        const sortBy = req.query.sortBy as string || 'totalSpent'; // totalSpent, totalPurchases, lastPurchase

        const mockTopCustomers = [
            {
                id: 1,
                name: 'John Doe',
                phone: '+91-9876543210',
                totalPurchases: 15,
                totalSpent: 4500.50,
                lastPurchase: '2024-01-20T10:30:00Z'
            },
            {
                id: 2,
                name: 'Jane Smith',
                phone: '+91-9876543211',
                totalPurchases: 8,
                totalSpent: 2400.75,
                lastPurchase: '2024-01-19T15:45:00Z'
            }
        ].slice(0, limit);

        res.json(successResponse('Top customers retrieved successfully', {
            customers: mockTopCustomers,
            sortBy,
            limit
        }));
    }));

    return router;
};

/**
 * Customers routes configuration
 */
const customersRoutes: FeatureRoutes = {
    config: {
        path: '/api/v1/customers',
        middleware: [detailedLogger('customers')],
        description: 'Customer management routes',
        version: '1.0.0'
    },
    setup: setupCustomersRoutes
};

export default customersRoutes;
