import { Router, Request, Response } from 'express';
import { FeatureRoutes, PaginationParams } from '../../types/routes.types';
import { asyncHandler } from '../../middlewares/errorHandler';
import { detailedLogger } from '../../middlewares/routeLogger';
import { successResponse, errorResponse } from '../index';

/**
 * Products routes setup
 */
const setupProductsRoutes = (): Router => {
    const router = Router();

    // Get all products with pagination
    router.get('/', asyncHandler(async (req: Request, res: Response) => {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        const search = req.query.search as string || '';
        const category = req.query.category as string || '';

        // Mock products data
        const mockProducts = [
            {
                id: 1,
                name: '<PERSON> (Basmati)',
                category: 'Grains',
                price: 120.00,
                stock: 50,
                unit: 'kg',
                barcode: '1234567890123',
                createdAt: new Date().toISOString()
            },
            {
                id: 2,
                name: 'Milk (Full Cream)',
                category: 'Dairy',
                price: 60.00,
                stock: 25,
                unit: 'liter',
                barcode: '1234567890124',
                createdAt: new Date().toISOString()
            }
        ];

        // Apply filters (mock implementation)
        let filteredProducts = mockProducts;
        if (search) {
            filteredProducts = mockProducts.filter(p =>
                p.name.toLowerCase().includes(search.toLowerCase())
            );
        }
        if (category) {
            filteredProducts = filteredProducts.filter(p =>
                p.category.toLowerCase() === category.toLowerCase()
            );
        }

        const total = filteredProducts.length;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

        res.json(successResponse('Products retrieved successfully', {
            products: paginatedProducts,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: endIndex < total,
                hasPrev: page > 1
            }
        }));
    }));

    // Get product by ID
    router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;

        // Mock product data
        const mockProduct = {
            id: parseInt(id),
            name: 'Rice (Basmati)',
            category: 'Grains',
            price: 120.00,
            stock: 50,
            unit: 'kg',
            barcode: '1234567890123',
            description: 'Premium quality basmati rice',
            supplier: 'ABC Suppliers',
            minStock: 10,
            maxStock: 100,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        res.json(successResponse('Product retrieved successfully', mockProduct));
    }));

    // Create new product
    router.post('/', asyncHandler(async (req: Request, res: Response) => {
        const { name, category, price, stock, unit, barcode, description, supplier } = req.body;

        // Validation
        if (!name || !category || !price || !stock || !unit) {
            return res.status(400).json(
                errorResponse('Name, category, price, stock, and unit are required')
            );
        }

        // Mock created product
        const newProduct = {
            id: Date.now(), // Mock ID generation
            name,
            category,
            price: parseFloat(price),
            stock: parseInt(stock),
            unit,
            barcode: barcode || `${Date.now()}`,
            description: description || '',
            supplier: supplier || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        res.status(201).json(successResponse('Product created successfully', newProduct));
    }));

    // Update product
    router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;
        const updateData = req.body;

        // Mock updated product
        const updatedProduct = {
            id: parseInt(id),
            ...updateData,
            updatedAt: new Date().toISOString()
        };

        res.json(successResponse('Product updated successfully', updatedProduct));
    }));

    // Delete product
    router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;

        // TODO: Implement actual deletion logic
        res.json(successResponse(`Product with ID ${id} deleted successfully`));
    }));

    // Get product categories
    router.get('/categories/list', asyncHandler(async (req: Request, res: Response) => {
        const mockCategories = [
            'Grains',
            'Dairy',
            'Vegetables',
            'Fruits',
            'Snacks',
            'Beverages',
            'Personal Care',
            'Household'
        ];

        res.json(successResponse('Categories retrieved successfully', mockCategories));
    }));

    // Search products by barcode
    router.get('/barcode/:barcode', asyncHandler(async (req: Request, res: Response) => {
        const { barcode } = req.params;

        // Mock product search by barcode
        const mockProduct = {
            id: 1,
            name: 'Rice (Basmati)',
            category: 'Grains',
            price: 120.00,
            stock: 50,
            unit: 'kg',
            barcode,
            createdAt: new Date().toISOString()
        };

        res.json(successResponse('Product found', mockProduct));
    }));

    return router;
};

/**
 * Products routes configuration
 */
const productsRoutes: FeatureRoutes = {
    config: {
        path: '/api/v1/products',
        middleware: [detailedLogger('products')],
        description: 'Product management routes',
        version: '1.0.0'
    },
    setup: setupProductsRoutes
};

export default productsRoutes;
