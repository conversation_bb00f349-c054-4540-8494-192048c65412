import { Router, Request, Response, NextFunction } from 'express';

/**
 * Interface for route configuration
 */
export interface RouteConfig {
    path: string;
    middleware?: Array<(req: Request, res: Response, next: NextFunction) => void>;
    description?: string;
    version?: string;
}

/**
 * Interface for feature route modules
 */
export interface FeatureRoutes {
    config: RouteConfig;
    setup: () => Router;
}

/**
 * Interface for API response structure
 */
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    timestamp: string;
}

/**
 * Interface for pagination
 */
export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

/**
 * Interface for paginated response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

/**
 * Route metadata for documentation
 */
export interface RouteMetadata {
    method: string;
    path: string;
    description: string;
    parameters?: Array<{
        name: string;
        type: string;
        required: boolean;
        description: string;
    }>;
    responses?: Array<{
        status: number;
        description: string;
        example?: any;
    }>;
}

/**
 * Feature module structure
 */
export interface FeatureModule {
    name: string;
    basePath: string;
    routes: RouteMetadata[];
    middleware?: string[];
}
