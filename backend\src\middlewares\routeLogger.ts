import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to log incoming requests
 */
export const routeLogger = (req: Request, res: Response, next: NextFunction): void => {
    const timestamp = new Date().toISOString();
    const method = req.method;
    const url = req.originalUrl;
    const userAgent = req.get('User-Agent') || 'Unknown';
    const ip = req.ip || req.connection.remoteAddress || 'Unknown';

    // Log the incoming request
    console.log(`📝 [${timestamp}] ${method} ${url} - IP: ${ip} - UA: ${userAgent}`);

    // Log request body for POST/PUT/PATCH requests (excluding sensitive data)
    if (['POST', 'PUT', 'PATCH'].includes(method) && req.body) {
        const sanitizedBody = { ...req.body };
        
        // Remove sensitive fields
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
        sensitiveFields.forEach(field => {
            if (sanitizedBody[field]) {
                sanitizedBody[field] = '[REDACTED]';
            }
        });

        console.log(`📦 Request Body:`, JSON.stringify(sanitizedBody, null, 2));
    }

    // Log query parameters if present
    if (Object.keys(req.query).length > 0) {
        console.log(`🔍 Query Params:`, req.query);
    }

    // Capture response time
    const startTime = Date.now();

    // Override res.json to log response
    const originalJson = res.json;
    res.json = function(body: any) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log(`✅ [${timestamp}] ${method} ${url} - ${res.statusCode} - ${responseTime}ms`);
        
        // Log response body for errors or if in development
        if (res.statusCode >= 400 || process.env.NODE_ENV === 'development') {
            console.log(`📤 Response:`, JSON.stringify(body, null, 2));
        }

        return originalJson.call(this, body);
    };

    next();
};

/**
 * Enhanced logger for specific routes
 */
export const detailedLogger = (feature: string) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        console.log(`🎯 [${feature.toUpperCase()}] ${req.method} ${req.originalUrl}`);
        next();
    };
};
