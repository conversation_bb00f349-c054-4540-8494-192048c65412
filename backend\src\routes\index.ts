import { Express, Router } from 'express';
import { RouteLoader } from '../utils/routeLoader';
import { ApiResponse } from '../types/routes.types';

/**
 * Setup all routes for the application
 */
export const setupRoutes = async (app: Express): Promise<void> => {
    console.log('🚀 Setting up routes...');

    // Create API router for versioning
    const apiRouter = Router();

    // API info endpoint
    apiRouter.get('/', (req, res) => {
        const response: ApiResponse = {
            success: true,
            message: 'Kirana Shop API',
            data: {
                version: '1.0.0',
                description: 'RESTful API for Kirana Shop Management System',
                endpoints: {
                    health: '/health',
                    api: '/api/v1',
                    docs: '/api/v1/docs'
                }
            },
            timestamp: new Date().toISOString()
        };
        res.json(response);
    });

    // API documentation endpoint
    apiRouter.get('/docs', async (req, res) => {
        const routeLoader = new RouteLoader(app);
        const documentation = routeLoader.getRoutesDocumentation();

        const response: ApiResponse = {
            success: true,
            message: 'API Documentation',
            data: {
                ...documentation,
                baseUrl: `${req.protocol}://${req.get('host')}/api/v1`,
                availableEndpoints: [
                    'GET /api/v1 - API information',
                    'GET /api/v1/docs - This documentation',
                    'GET /health - Health check'
                ]
            },
            timestamp: new Date().toISOString()
        };

        res.json(response);
    });

    // Mount API router
    app.use('/api/v1', apiRouter);

    // Load feature routes dynamically
    const routeLoader = new RouteLoader(app);
    await routeLoader.loadRoutes();

    console.log('✅ Routes setup completed');
};


