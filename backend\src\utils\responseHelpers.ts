import { ApiResponse } from '../types/routes.types';

/**
 * Create a standardized API response
 */
export const createApiResponse = <T>(
    success: boolean,
    message: string,
    data?: T,
    error?: string
): ApiResponse<T> => {
    return {
        success,
        message,
        data,
        error,
        timestamp: new Date().toISOString()
    };
};

/**
 * Success response helper
 */
export const successResponse = <T>(message: string, data?: T): ApiResponse<T> => {
    return createApiResponse(true, message, data);
};

/**
 * Error response helper
 */
export const errorResponse = (message: string, error?: string): ApiResponse => {
    return createApiResponse(false, message, undefined, error);
};
