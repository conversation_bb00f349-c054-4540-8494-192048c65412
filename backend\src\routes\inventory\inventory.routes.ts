import { Router, Request, Response } from 'express';
import { FeatureRoutes } from '../../types/routes.types';
import { asyncHandler } from '../../middlewares/errorHandler';
import { detailedLogger } from '../../middlewares/routeLogger';
import { successResponse, errorResponse } from '../../utils/responseHelpers';

/**
 * Inventory routes setup
 */
const setupInventoryRoutes = (): Router => {
    const router = Router();

    // Get inventory overview
    router.get('/overview', asyncHandler(async (req: Request, res: Response) => {
        const mockOverview = {
            totalProducts: 150,
            lowStockItems: 12,
            outOfStockItems: 3,
            totalValue: 125000.50,
            categories: {
                'Grains': 25,
                'Dairy': 15,
                'Vegetables': 30,
                'Fruits': 20,
                'Snacks': 35,
                'Beverages': 25
            },
            recentActivity: [
                {
                    id: 1,
                    type: 'stock_in',
                    product: 'Rice (<PERSON><PERSON><PERSON>)',
                    quantity: 20,
                    timestamp: new Date().toISOString()
                },
                {
                    id: 2,
                    type: 'stock_out',
                    product: 'Milk (Full Cream)',
                    quantity: 5,
                    timestamp: new Date().toISOString()
                }
            ]
        };

        res.json(successResponse('Inventory overview retrieved successfully', mockOverview));
    }));

    // Get low stock items
    router.get('/low-stock', asyncHandler(async (req: Request, res: Response) => {
        const threshold = parseInt(req.query.threshold as string) || 10;

        const mockLowStockItems = [
            {
                id: 1,
                name: 'Rice (Basmati)',
                currentStock: 8,
                minStock: 10,
                category: 'Grains',
                unit: 'kg'
            },
            {
                id: 2,
                name: 'Cooking Oil',
                currentStock: 5,
                minStock: 15,
                category: 'Cooking',
                unit: 'liter'
            }
        ];

        res.json(successResponse('Low stock items retrieved successfully', mockLowStockItems));
    }));

    // Get out of stock items
    router.get('/out-of-stock', asyncHandler(async (req: Request, res: Response) => {
        const mockOutOfStockItems = [
            {
                id: 3,
                name: 'Sugar',
                currentStock: 0,
                minStock: 20,
                category: 'Grains',
                unit: 'kg',
                lastRestocked: '2024-01-15T10:30:00Z'
            }
        ];

        res.json(successResponse('Out of stock items retrieved successfully', mockOutOfStockItems));
    }));

    // Stock adjustment (add/remove stock)
    router.post('/adjust', asyncHandler(async (req: Request, res: Response) => {
        const { productId, quantity, type, reason, notes } = req.body;

        if (!productId || !quantity || !type) {
            return res.status(400).json(
                errorResponse('Product ID, quantity, and type (in/out) are required')
            );
        }

        if (!['in', 'out'].includes(type)) {
            return res.status(400).json(
                errorResponse('Type must be either "in" or "out"')
            );
        }

        // Mock stock adjustment
        const adjustment = {
            id: Date.now(),
            productId: parseInt(productId),
            quantity: parseInt(quantity),
            type,
            reason: reason || 'Manual adjustment',
            notes: notes || '',
            adjustedBy: 'Admin', // TODO: Get from authenticated user
            timestamp: new Date().toISOString()
        };

        res.status(201).json(successResponse('Stock adjusted successfully', adjustment));
    }));

    // Get stock movements/history
    router.get('/movements', asyncHandler(async (req: Request, res: Response) => {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 20;
        const productId = req.query.productId as string;
        const type = req.query.type as string;

        const mockMovements = [
            {
                id: 1,
                productId: 1,
                productName: 'Rice (Basmati)',
                type: 'in',
                quantity: 50,
                reason: 'New stock purchase',
                adjustedBy: 'Admin',
                timestamp: '2024-01-20T10:30:00Z'
            },
            {
                id: 2,
                productId: 1,
                productName: 'Rice (Basmati)',
                type: 'out',
                quantity: 5,
                reason: 'Sale',
                adjustedBy: 'Cashier',
                timestamp: '2024-01-20T14:15:00Z'
            }
        ];

        // Apply filters
        let filteredMovements = mockMovements;
        if (productId) {
            filteredMovements = mockMovements.filter(m => m.productId === parseInt(productId));
        }
        if (type) {
            filteredMovements = filteredMovements.filter(m => m.type === type);
        }

        const total = filteredMovements.length;
        const startIndex = (page - 1) * limit;
        const paginatedMovements = filteredMovements.slice(startIndex, startIndex + limit);

        res.json(successResponse('Stock movements retrieved successfully', {
            movements: paginatedMovements,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: startIndex + limit < total,
                hasPrev: page > 1
            }
        }));
    }));

    // Bulk stock update
    router.post('/bulk-update', asyncHandler(async (req: Request, res: Response) => {
        const { updates } = req.body;

        if (!updates || !Array.isArray(updates)) {
            return res.status(400).json(
                errorResponse('Updates array is required')
            );
        }

        // Validate each update
        for (const update of updates) {
            if (!update.productId || !update.quantity || !update.type) {
                return res.status(400).json(
                    errorResponse('Each update must have productId, quantity, and type')
                );
            }
        }

        // Mock bulk update processing
        const processedUpdates = updates.map((update: any) => ({
            ...update,
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            status: 'completed'
        }));

        res.json(successResponse('Bulk stock update completed', {
            totalUpdates: updates.length,
            successful: updates.length,
            failed: 0,
            updates: processedUpdates
        }));
    }));

    return router;
};

/**
 * Inventory routes configuration
 */
const inventoryRoutes: FeatureRoutes = {
    config: {
        path: '/api/v1/inventory',
        middleware: [detailedLogger('inventory')],
        description: 'Inventory management routes',
        version: '1.0.0'
    },
    setup: setupInventoryRoutes
};

export default inventoryRoutes;
