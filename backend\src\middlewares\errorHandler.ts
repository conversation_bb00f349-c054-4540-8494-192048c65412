import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types/routes.types';

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
    public statusCode: number;
    public isOperational: boolean;

    constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;

        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * Global error handling middleware
 */
export const errorHandler = (
    error: Error | ApiError,
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    let statusCode = 500;
    let message = 'Internal Server Error';
    let isOperational = false;

    // Handle different types of errors
    if (error instanceof ApiError) {
        statusCode = error.statusCode;
        message = error.message;
        isOperational = error.isOperational;
    } else if (error.name === 'ValidationError') {
        statusCode = 400;
        message = 'Validation Error: ' + error.message;
    } else if (error.name === 'CastError') {
        statusCode = 400;
        message = 'Invalid ID format';
    } else if (error.name === 'MongoError' && (error as any).code === 11000) {
        statusCode = 409;
        message = 'Duplicate field value';
    } else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = 'Invalid token';
    } else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = 'Token expired';
    }

    // Log error details
    const timestamp = new Date().toISOString();
    console.error(`❌ [${timestamp}] Error in ${req.method} ${req.originalUrl}:`);
    console.error(`   Status: ${statusCode}`);
    console.error(`   Message: ${message}`);
    
    if (process.env.NODE_ENV === 'development') {
        console.error(`   Stack: ${error.stack}`);
    }

    // Prepare error response
    const errorResponse: ApiResponse = {
        success: false,
        message,
        timestamp
    };

    // Include stack trace in development
    if (process.env.NODE_ENV === 'development') {
        (errorResponse as any).stack = error.stack;
        (errorResponse as any).error = error.name;
    }

    // Send error response
    res.status(statusCode).json(errorResponse);
};

/**
 * Async error wrapper to catch async errors
 */
export const asyncHandler = (fn: Function) => {
    return (req: Request, res: Response, next: NextFunction) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};

/**
 * Not found error handler
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
    const error = new ApiError(`Route ${req.method} ${req.originalUrl} not found`, 404);
    next(error);
};

/**
 * Validation error helper
 */
export const createValidationError = (message: string): ApiError => {
    return new ApiError(message, 400);
};

/**
 * Unauthorized error helper
 */
export const createUnauthorizedError = (message: string = 'Unauthorized'): ApiError => {
    return new ApiError(message, 401);
};

/**
 * Forbidden error helper
 */
export const createForbiddenError = (message: string = 'Forbidden'): ApiError => {
    return new ApiError(message, 403);
};

/**
 * Not found error helper
 */
export const createNotFoundError = (message: string = 'Resource not found'): ApiError => {
    return new ApiError(message, 404);
};
