import { Router, Request, Response } from 'express';
import { FeatureRoutes } from '../../types/routes.types';
import { asyncHandler } from '../../middlewares/errorHandler';
import { detailedLogger } from '../../middlewares/routeLogger';
import { successResponse, errorResponse } from '../index';

/**
 * Sales routes setup
 */
const setupSalesRoutes = (): Router => {
    const router = Router();

    // Get all sales with pagination
    router.get('/', asyncHandler(async (req: Request, res: Response) => {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        const startDate = req.query.startDate as string;
        const endDate = req.query.endDate as string;

        const mockSales = [
            {
                id: 1,
                invoiceNumber: 'INV-001',
                customerId: 1,
                customerName: '<PERSON>',
                items: [
                    { productId: 1, productName: 'Rice (Basmati)', quantity: 2, price: 120, total: 240 },
                    { productId: 2, productName: 'Milk', quantity: 1, price: 60, total: 60 }
                ],
                subtotal: 300,
                tax: 30,
                discount: 0,
                total: 330,
                paymentMethod: 'cash',
                status: 'completed',
                createdAt: '2024-01-20T10:30:00Z'
            },
            {
                id: 2,
                invoiceNumber: 'INV-002',
                customerId: 2,
                customerName: 'Jane Smith',
                items: [
                    { productId: 3, productName: 'Sugar', quantity: 1, price: 45, total: 45 }
                ],
                subtotal: 45,
                tax: 4.5,
                discount: 5,
                total: 44.5,
                paymentMethod: 'card',
                status: 'completed',
                createdAt: '2024-01-20T11:15:00Z'
            }
        ];

        const total = mockSales.length;
        const startIndex = (page - 1) * limit;
        const paginatedSales = mockSales.slice(startIndex, startIndex + limit);

        res.json(successResponse('Sales retrieved successfully', {
            sales: paginatedSales,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: startIndex + limit < total,
                hasPrev: page > 1
            }
        }));
    }));

    // Get sale by ID
    router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;

        const mockSale = {
            id: parseInt(id),
            invoiceNumber: 'INV-001',
            customerId: 1,
            customerName: 'John Doe',
            customerPhone: '+91-9876543210',
            items: [
                { 
                    productId: 1, 
                    productName: 'Rice (Basmati)', 
                    quantity: 2, 
                    price: 120, 
                    total: 240,
                    unit: 'kg'
                },
                { 
                    productId: 2, 
                    productName: 'Milk', 
                    quantity: 1, 
                    price: 60, 
                    total: 60,
                    unit: 'liter'
                }
            ],
            subtotal: 300,
            tax: 30,
            discount: 0,
            total: 330,
            paymentMethod: 'cash',
            status: 'completed',
            notes: 'Regular customer',
            createdAt: '2024-01-20T10:30:00Z',
            updatedAt: '2024-01-20T10:30:00Z'
        };

        res.json(successResponse('Sale retrieved successfully', mockSale));
    }));

    // Create new sale
    router.post('/', asyncHandler(async (req: Request, res: Response) => {
        const { customerId, customerName, items, paymentMethod, discount, notes } = req.body;

        // Validation
        if (!items || !Array.isArray(items) || items.length === 0) {
            return res.status(400).json(
                errorResponse('Items array is required and cannot be empty')
            );
        }

        if (!paymentMethod) {
            return res.status(400).json(
                errorResponse('Payment method is required')
            );
        }

        // Calculate totals
        const subtotal = items.reduce((sum: number, item: any) => sum + (item.quantity * item.price), 0);
        const tax = subtotal * 0.1; // 10% tax
        const discountAmount = discount || 0;
        const total = subtotal + tax - discountAmount;

        // Mock created sale
        const newSale = {
            id: Date.now(),
            invoiceNumber: `INV-${String(Date.now()).slice(-6)}`,
            customerId: customerId || null,
            customerName: customerName || 'Walk-in Customer',
            items,
            subtotal,
            tax,
            discount: discountAmount,
            total,
            paymentMethod,
            status: 'completed',
            notes: notes || '',
            createdAt: new Date().toISOString()
        };

        res.status(201).json(successResponse('Sale created successfully', newSale));
    }));

    // Update sale status
    router.patch('/:id/status', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;
        const { status } = req.body;

        const validStatuses = ['pending', 'completed', 'cancelled', 'refunded'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json(
                errorResponse(`Status must be one of: ${validStatuses.join(', ')}`)
            );
        }

        // Mock status update
        const updatedSale = {
            id: parseInt(id),
            status,
            updatedAt: new Date().toISOString()
        };

        res.json(successResponse('Sale status updated successfully', updatedSale));
    }));

    // Get sales analytics
    router.get('/analytics/summary', asyncHandler(async (req: Request, res: Response) => {
        const period = req.query.period as string || 'today'; // today, week, month, year

        const mockAnalytics = {
            period,
            totalSales: 15,
            totalRevenue: 4500.50,
            averageOrderValue: 300.03,
            topProducts: [
                { productId: 1, productName: 'Rice (Basmati)', quantitySold: 25, revenue: 3000 },
                { productId: 2, productName: 'Milk', quantitySold: 20, revenue: 1200 }
            ],
            paymentMethods: {
                cash: { count: 10, amount: 3000 },
                card: { count: 4, amount: 1200 },
                upi: { count: 1, amount: 300.50 }
            },
            hourlyDistribution: [
                { hour: 9, sales: 2, revenue: 600 },
                { hour: 10, sales: 3, revenue: 900 },
                { hour: 11, sales: 5, revenue: 1500 }
            ]
        };

        res.json(successResponse('Sales analytics retrieved successfully', mockAnalytics));
    }));

    // Process refund
    router.post('/:id/refund', asyncHandler(async (req: Request, res: Response) => {
        const { id } = req.params;
        const { reason, amount, items } = req.body;

        if (!reason) {
            return res.status(400).json(
                errorResponse('Refund reason is required')
            );
        }

        // Mock refund processing
        const refund = {
            id: Date.now(),
            saleId: parseInt(id),
            reason,
            amount: amount || 0,
            items: items || [],
            processedBy: 'Admin', // TODO: Get from authenticated user
            processedAt: new Date().toISOString(),
            status: 'completed'
        };

        res.json(successResponse('Refund processed successfully', refund));
    }));

    // Get daily sales report
    router.get('/reports/daily', asyncHandler(async (req: Request, res: Response) => {
        const date = req.query.date as string || new Date().toISOString().split('T')[0];

        const mockDailyReport = {
            date,
            totalSales: 15,
            totalRevenue: 4500.50,
            totalItems: 45,
            averageOrderValue: 300.03,
            paymentBreakdown: {
                cash: 3000,
                card: 1200,
                upi: 300.50
            },
            hourlyBreakdown: Array.from({ length: 12 }, (_, i) => ({
                hour: i + 9,
                sales: Math.floor(Math.random() * 5),
                revenue: Math.floor(Math.random() * 1000)
            }))
        };

        res.json(successResponse('Daily sales report retrieved successfully', mockDailyReport));
    }));

    return router;
};

/**
 * Sales routes configuration
 */
const salesRoutes: FeatureRoutes = {
    config: {
        path: '/api/v1/sales',
        router: setupSalesRoutes(),
        middleware: [detailedLogger('sales')],
        description: 'Sales management routes',
        version: '1.0.0'
    },
    setup: setupSalesRoutes
};

export default salesRoutes;
